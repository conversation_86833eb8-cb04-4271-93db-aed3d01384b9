#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多进程下载功能的脚本
"""

import os
import sys
import tempfile
import requests
from unittest.mock import patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_chunk_download():
    """测试块下载功能"""
    print("开始测试块下载功能...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"临时目录: {temp_dir}")
        
        # 模拟切片URL列表
        test_urls = [
            "https://httpbin.org/bytes/1024",  # 1KB
            "https://httpbin.org/bytes/2048",  # 2KB  
            "https://httpbin.org/bytes/4096",  # 4KB
        ]
        
        # 创建一个简化的下载器类用于测试
        class TestDownloader:
            def download_chunk_to_file(self, chunk_id, ts_url_list, temp_dir):
                """简化的块下载方法"""
                chunk_file_path = os.path.join(temp_dir, f'chunk_{chunk_id}.tmp')
                
                try:
                    with open(chunk_file_path, 'wb') as chunk_file:
                        for ts_url in ts_url_list:
                            try:
                                print(f"下载: {ts_url}")
                                response = requests.get(ts_url, timeout=10)
                                video_data = response.content
                                chunk_file.write(video_data)
                                print(f"下载完成，大小: {len(video_data)} 字节")
                            except Exception as e:
                                print(f"下载失败 {ts_url}: {e}")
                except Exception as e:
                    print(f"创建块文件失败 {chunk_file_path}: {e}")
                    raise
            
            def merge_chunk_files(self, temp_dir, chunk_count, final_file_path):
                """合并块文件"""
                try:
                    with open(final_file_path, 'wb') as final_file:
                        for chunk_id in range(chunk_count):
                            chunk_file_path = os.path.join(temp_dir, f'chunk_{chunk_id}.tmp')
                            
                            if not os.path.exists(chunk_file_path):
                                print(f"警告: 块文件 {chunk_file_path} 不存在")
                                continue
                            
                            with open(chunk_file_path, 'rb') as chunk_file:
                                while True:
                                    data = chunk_file.read(8192)
                                    if not data:
                                        break
                                    final_file.write(data)
                    return True
                except Exception as e:
                    print(f"合并块文件时发生错误: {e}")
                    return False
        
        # 创建测试下载器
        downloader = TestDownloader()
        
        # 测试下载单个块
        print("\n测试下载单个块...")
        downloader.download_chunk_to_file(0, test_urls, temp_dir)
        
        # 检查块文件是否创建
        chunk_file = os.path.join(temp_dir, 'chunk_0.tmp')
        if os.path.exists(chunk_file):
            size = os.path.getsize(chunk_file)
            print(f"块文件创建成功，大小: {size} 字节")
        else:
            print("错误: 块文件未创建")
            return False
        
        # 测试合并功能
        print("\n测试合并功能...")
        final_file = os.path.join(temp_dir, 'test_final.mp4')
        success = downloader.merge_chunk_files(temp_dir, 1, final_file)
        
        if success and os.path.exists(final_file):
            final_size = os.path.getsize(final_file)
            print(f"合并成功，最终文件大小: {final_size} 字节")
            return True
        else:
            print("错误: 合并失败")
            return False

def test_size_flexibility():
    """测试不同大小切片的处理能力"""
    print("\n开始测试大小灵活性...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 模拟不同大小的切片
        test_data = [
            b'a' * 1024,      # 1KB
            b'b' * (3 * 1024 * 1024),  # 3MB (超过原来的2MB限制)
            b'c' * 512,       # 512B
            b'd' * (5 * 1024 * 1024),  # 5MB (远超原来的2MB限制)
        ]
        
        # 创建模拟的块文件
        for i, data in enumerate(test_data):
            chunk_file = os.path.join(temp_dir, f'chunk_{i}.tmp')
            with open(chunk_file, 'wb') as f:
                f.write(data)
            print(f"创建块 {i}，大小: {len(data)} 字节")
        
        # 测试合并
        class TestDownloader:
            def merge_chunk_files(self, temp_dir, chunk_count, final_file_path):
                try:
                    with open(final_file_path, 'wb') as final_file:
                        for chunk_id in range(chunk_count):
                            chunk_file_path = os.path.join(temp_dir, f'chunk_{chunk_id}.tmp')
                            
                            if not os.path.exists(chunk_file_path):
                                print(f"警告: 块文件 {chunk_file_path} 不存在")
                                continue
                            
                            with open(chunk_file_path, 'rb') as chunk_file:
                                while True:
                                    data = chunk_file.read(8192)
                                    if not data:
                                        break
                                    final_file.write(data)
                    return True
                except Exception as e:
                    print(f"合并块文件时发生错误: {e}")
                    return False
        
        downloader = TestDownloader()
        final_file = os.path.join(temp_dir, 'test_large.mp4')
        success = downloader.merge_chunk_files(temp_dir, len(test_data), final_file)
        
        if success:
            final_size = os.path.getsize(final_file)
            expected_size = sum(len(data) for data in test_data)
            print(f"合并成功！")
            print(f"期望大小: {expected_size} 字节")
            print(f"实际大小: {final_size} 字节")
            
            if final_size == expected_size:
                print("✅ 大小验证通过")
                return True
            else:
                print("❌ 大小验证失败")
                return False
        else:
            print("❌ 合并失败")
            return False

if __name__ == "__main__":
    print("=" * 50)
    print("多进程下载功能测试")
    print("=" * 50)
    
    # 测试基本功能
    test1_result = test_chunk_download()
    
    # 测试大小灵活性
    test2_result = test_size_flexibility()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"基本功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"大小灵活性测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！新的多进程下载方案可以处理任意大小的切片。")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")
    print("=" * 50)
