#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音直播回放下载Web界面
"""

from flask import Flask, request, jsonify, render_template_string, redirect, url_for
from flask_socketio import SocketIO, emit
import json
import os
import re
import threading
import concurrent.futures
from config import USER_CONFIGS, ACTIVE_USER, DOWNLOAD_CONFIG
from main import create_downloader
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'douyin_download_secret'
socketio = SocketIO(app, cors_allowed_origins="*")

# 配置文件路径
CONFIG_FILE = 'config.py'

# 存储下载任务的状态
download_tasks = {}

# 读取配置文件
def load_config():
    # 重新加载配置
    import importlib
    import config
    importlib.reload(config)
    return config.USER_CONFIGS, config.ACTIVE_USER, config.DOWNLOAD_CONFIG

# 保存配置到文件
def save_user_configs(user_configs):
    # 读取原配置文件内容
    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 构造新的 USER_CONFIGS 部分
    config_str = "USER_CONFIGS = {\n"
    for user_name, user_data in user_configs.items():
        # 对cookie中的特殊字符进行转义
        escaped_cookie = user_data['cookie'].replace('\\', '\\\\').replace('"', '\\"').replace("'", "\\'")
        config_str += f"    '{user_name}': {{\n"
        config_str += f"        'cookie': '{escaped_cookie}'\n"
        config_str += "    },\n"
    config_str += "}\n"
    
    # 替换原配置
    # 查找USER_CONFIGS开始位置
    lines = content.split('\n')
    new_lines = []
    skip = False
    i = 0
    while i < len(lines):
        line = lines[i]
        if line.strip().startswith('USER_CONFIGS = {'):
            skip = True
            # 添加新的配置
            new_lines.append(config_str.rstrip())
            # 跳过原来的配置
            brace_count = 1
            i += 1
            while i < len(lines) and brace_count > 0:
                line = lines[i]
                brace_count += line.count('{') - line.count('}')
                i += 1
            # 添加下一个非空行（通常是注释或ACTIVE_USER）
            if i < len(lines):
                # 添加ACTIVE_USER行
                while i < len(lines) and (lines[i].strip() == '' or lines[i].strip().startswith('#')):
                    new_lines.append(lines[i])
                    i += 1
                if i < len(lines) and lines[i].strip().startswith('ACTIVE_USER'):
                    new_lines.append(lines[i])
                    i += 1
                elif i < len(lines):
                    # 如果没有ACTIVE_USER行，添加一个空行
                    new_lines.append('')
            skip = False
        else:
            new_lines.append(line)
            i += 1
    
    new_content = '\n'.join(new_lines)
    
    # 写入文件
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        f.write(new_content)

# 主页 - 显示用户列表和下载选项
@app.route('/')
def index():
    user_configs, active_user, download_config = load_config()
    
    # 如果配置中的日期未设置，则使用今天的日期
    today = datetime.now().strftime('%Y-%m-%d')
    print(today)
    start_date = today
    end_date = today
    print(start_date)
    
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>抖音直播回放下载工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .btn { padding: 8px 16px; margin: 2px; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .form-container { background-color: #f8f9fa; padding: 20px; border-radius: 4px; margin: 20px 0; }
        .actions { text-align: center; margin: 20px 0; }
        .progress-bar-container { width: 100%; background-color: #f0f0f0; border-radius: 5px; margin: 10px 0; }
        .progress-bar { height: 20px; background-color: #4caf50; border-radius: 5px; text-align: center; line-height: 20px; color: white; }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <h1>抖音直播回放下载工具</h1>
        
        <h2>用户管理</h2>
        <table>
            <tr>
                <th>用户名</th>
                <th>操作</th>
            </tr>
            {% for user_name in user_configs.keys() %}
            <tr>
                <td>{{ user_name }} {% if user_name == active_user %}(默认){% endif %}</td>
                <td>
                    <a href="/edit_user/{{ user_name }}" class="btn btn-primary">编辑</a>
                    <a href="/delete_user/{{ user_name }}" class="btn btn-danger" onclick="return confirm('确定要删除用户 {{ user_name }} 吗？')">删除</a>
                </td>
            </tr>
            {% endfor %}
        </table>
        <a href="/add_user" class="btn btn-success">添加用户</a>
        
        <h2>下载回放视频</h2>
        <form action="/download" method="POST">
            <div class="form-group">
                <label for="user">选择用户:</label>
                <select id="user" name="user" required>
                    {% for user_name in user_configs.keys() %}
                    <option value="{{ user_name }}" {% if user_name == active_user %}selected{% endif %}>{{ user_name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label for="start_date">开始日期:</label>
                <input type="date" id="start_date" name="start_date" value="{{ start_date }}" required>
            </div>
            
            <div class="form-group">
                <label for="end_date">结束日期:</label>
                <input type="date" id="end_date" name="end_date" value="{{ end_date }}" required>
            </div>
            
            <div class="actions">
                <input type="submit" value="列出视频" class="btn btn-primary">
            </div>
        </form>
    </div>
</body>
</html>
''', user_configs=user_configs, active_user=active_user, download_config=download_config, today=today, start_date=start_date, end_date=end_date)

# 添加用户页面
@app.route('/add_user')
def add_user():
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加用户 - 抖音直播回放下载工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>添加用户</h1>
        <form action="/save_user" method="POST">
            <input type="hidden" name="old_name" value="">
            <div class="form-group">
                <label for="user_name">用户名:</label>
                <input type="text" id="user_name" name="user_name" required>
            </div>
            
            <div class="form-group">
                <label for="cookie">Cookie:</label>
                <textarea id="cookie" name="cookie" rows="10" required></textarea>
            </div>
            
            <div class="form-group">
                <input type="submit" value="保存" class="btn btn-primary">
                <a href="/" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>
</body>
</html>
''')

# 编辑用户页面
@app.route('/edit_user/<user_name>')
def edit_user(user_name):
    user_configs, _, _ = load_config()
    if user_name not in user_configs:
        return "用户不存在", 404
    
    user_data = user_configs[user_name]
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>编辑用户 - 抖音直播回放下载工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>编辑用户</h1>
        <form action="/save_user" method="POST">
            <input type="hidden" name="old_name" value="{{ user_name }}">
            <div class="form-group">
                <label for="user_name">用户名:</label>
                <input type="text" id="user_name" name="user_name" value="{{ user_name }}" required>
            </div>
            
            <div class="form-group">
                <label for="cookie">Cookie:</label>
                <textarea id="cookie" name="cookie" rows="10" required>{{ user_data.cookie }}</textarea>
            </div>
            
            <div class="form-group">
                <input type="submit" value="保存" class="btn btn-primary">
                <a href="/" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>
</body>
</html>
''', user_name=user_name, user_data=user_data)

# 保存用户信息
@app.route('/save_user', methods=['POST'])
def save_user():
    old_name = request.form['old_name']
    user_name = request.form['user_name']
    cookie = request.form['cookie']
    
    user_configs, _, _ = load_config()
    
    # 如果是修改用户，先删除旧的用户信息
    if old_name and old_name in user_configs:
        del user_configs[old_name]
    
    # 添加或更新用户信息
    user_configs[user_name] = {
        'cookie': cookie
    }
    
    # 保存到配置文件
    save_user_configs(user_configs)
    
    return redirect(url_for('index'))

# 删除用户
@app.route('/delete_user/<user_name>')
def delete_user(user_name):
    user_configs, _, _ = load_config()
    
    if user_name in user_configs:
        del user_configs[user_name]
        save_user_configs(user_configs)
    
    return redirect(url_for('index'))

# 列出并选择要下载的视频
@app.route('/download', methods=['POST'])
def download():
    user_name = request.form['user']
    start_date = request.form['start_date']
    end_date = request.form['end_date']
    
    # 创建下载器实例
    downloader = create_downloader(user_name)
    
    # 获取历史回放列表
    history_list = downloader.list_replays_by_date(start_date, end_date)
    
    # 将history_list转换为列表以支持模板中的enumerate
    if history_list:
        history_items = list(enumerate(history_list))
    else:
        history_items = []
    
    return render_template_string('''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>选择视频 - 抖音直播回放下载工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .btn { padding: 8px 16px; margin: 2px; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .form-group { margin: 15px 0; }
        .actions { text-align: center; margin: 20px 0; }
        .progress-bar-container { width: 100%; background-color: #f0f0f0; border-radius: 5px; margin: 10px 0; }
        .progress-bar { height: 20px; background-color: #4caf50; border-radius: 5px; text-align: center; line-height: 20px; color: white; }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <h1>选择要下载的视频</h1>
        <p>用户: {{ user_name }} | 日期范围: {{ start_date }} 到 {{ end_date }}</p>
        
        {% if history_items %}
        <form id="downloadForm" action="/start_download" method="POST">
            <input type="hidden" name="user_name" value="{{ user_name }}">
            <input type="hidden" name="start_date" value="{{ start_date }}">
            <input type="hidden" name="end_date" value="{{ end_date }}">
            
            <table>
                <tr>
                    <th><input type="checkbox" id="select_all" onclick="toggleAll(this)"> 全选</th>
                    <th>序号</th>
                    <th>房间ID</th>
                    <th>开始时间</th>
                    <th>标题</th>
                </tr>
                {% for i, room in history_items %}
                <tr>
                    <td><input type="checkbox" name="selected_videos" value="{{ i }}"></td>
                    <td>{{ i+1 }}</td>
                    <td>{{ room.roomID }}</td>
                    <td>{{ room.startTime }}</td>
                    <td>{{ room.roomTitle }}</td>
                </tr>
                {% endfor %}
            </table>
            
            <div class="actions">
                <input type="submit" value="开始下载选中视频" class="btn btn-primary">
                <a href="/" class="btn btn-secondary">返回首页</a>
            </div>
        </form>
        
        <div id="progressContainer" style="display: none;">
            <h2>下载进度</h2>
            <div id="progressList"></div>
        </div>
        
        <script>
            const socket = io();
            
            function toggleAll(source) {
                checkboxes = document.getElementsByName('selected_videos');
                for(var i=0, n=checkboxes.length; i<n; i++) {
                    checkboxes[i].checked = source.checked;
                }
            }
            
            document.getElementById('downloadForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 显示进度区域
                document.getElementById('progressContainer').style.display = 'block';
                
                // 禁用提交按钮
                const submitBtn = document.querySelector('input[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.value = '下载中...';
                
                // 收集表单数据
                const formData = new FormData(this);
                const data = {};
                formData.forEach((value, key) => {
                    if (data[key]) {
                        if (Array.isArray(data[key])) {
                            data[key].push(value);
                        } else {
                            data[key] = [data[key], value];
                        }
                    } else {
                        data[key] = value;
                    }
                });
                
                // 发送下载请求
                fetch('/start_download', {
                    method: 'POST',
                    body: new URLSearchParams(data)
                });
            });
            
            // 监听进度更新
            socket.on('download_progress', function(data) {
                const progressList = document.getElementById('progressList');
                let progressItem = document.getElementById('progress-' + data.video_id);
                
                if (!progressItem) {
                    // 创建新的进度项
                    progressItem = document.createElement('div');
                    progressItem.id = 'progress-' + data.video_id;
                    progressItem.innerHTML = `
                        <h3>${data.title}</h3>
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="bar-${data.video_id}" style="width: 0%">0%</div>
                        </div>
                        <p id="status-${data.video_id}">准备中...</p>
                    `;
                    progressList.appendChild(progressItem);
                }
                
                // 更新进度条
                const progressBar = document.getElementById('bar-' + data.video_id);
                const statusText = document.getElementById('status-' + data.video_id);
                
                if (data.status === 'progress') {
                    progressBar.style.width = data.progress + '%';
                    progressBar.textContent = Math.round(data.progress) + '%';
                    statusText.textContent = `已下载 ${data.current} / ${data.total} 个切片`;
                } else if (data.status === 'completed') {
                    progressBar.style.width = '100%';
                    progressBar.textContent = '100%';
                    statusText.textContent = '下载完成';
                    statusText.style.color = 'green';
                } else if (data.status === 'error') {
                    statusText.textContent = '下载出错: ' + data.message;
                    statusText.style.color = 'red';
                } else if (data.status === 'merging') {
                    statusText.textContent = '正在合并视频文件...';
                }
            });
        </script>
        {% else %}
        <p>在指定日期范围内没有找到回放视频。</p>
        <div class="actions">
            <a href="/" class="btn btn-secondary">返回首页</a>
        </div>
        {% endif %}
    </div>
</body>
</html>
''', user_name=user_name, start_date=start_date, end_date=end_date, history_items=history_items)

# 自定义下载进度回调函数
def download_progress_callback(video_id, title, current, total):
    """下载进度回调函数"""
    progress = (current / total) * 100
    socketio.emit('download_progress', {
        'video_id': video_id,
        'title': title,
        'status': 'progress',
        'progress': progress,
        'current': current,
        'total': total
    })

# 修改下载器的下载方法以支持进度回调
def download_with_progress(downloader, room_id, title, start_time, video_id, output_path="./video", max_workers=16):
    """带进度回调的下载方法，使用线程替代多进程"""
    try:
        # 获取房间信息
        room_info = downloader.get_room_info(room_id)
        
        if not room_info or room_info.get("status_code") != 0:
            socketio.emit('download_progress', {
                'video_id': video_id,
                'title': title,
                'status': 'error',
                'message': '获取房间信息失败'
            })
            return False
            
        # 解析播放信息
        data = room_info.get("data", {})
        play_info = data.get("play_info", [])
        
        if not play_info:
            socketio.emit('download_progress', {
                'video_id': video_id,
                'title': title,
                'status': 'error',
                'message': '未找到回放视频信息'
            })
            return False
            
        # 获取播放链接
        m3u8_url = play_info[0].get("hls_url") or play_info[0].get("flv_url")
        if not m3u8_url:
            socketio.emit('download_progress', {
                'video_id': video_id,
                'title': title,
                'status': 'error',
                'message': '未找到有效的视频播放链接'
            })
            return False
            
        # 创建输出目录
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # 生成文件名（用户名-视频的时间-标题）
        # 清理标题中的非法字符
        import re
        safe_title = re.sub(r'[\\/:*?"<>|]', '_', title)
        filename = f"{downloader.user_name}-{start_time}-{safe_title}"
        file_path = os.path.join(output_path, filename)
        
        try:
            # 获取m3u8文件切片
            ts_list = downloader.get_ts_list(m3u8_url)
            just_domain = ts_list[0][0] == '/'
            base_url = downloader.get_base_url(m3u8_url, just_domain) + '/'
            ts_list = [s.lstrip('/') for s in ts_list]
            ts_url_list = [base_url + item for item in ts_list]

            # merged_data用于临时保存下载好的切片数据
            from io import BytesIO
            merged_data = BytesIO()

            # 使用线程池并行下载切片文件
            total = len(ts_url_list)
            downloaded = 0
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有下载任务
                future_to_url = {executor.submit(downloader.download_ts, url): url for url in ts_url_list}
                
                # 处理完成的任务
                for future in concurrent.futures.as_completed(future_to_url):
                    try:
                        video_data = future.result()
                        merged_data.write(video_data[0])
                        downloaded += 1
                        
                        # 发送进度更新
                        download_progress_callback(video_id, title, downloaded, total)
                    except Exception as e:
                        print(f"下载切片失败: {e}")
                        merged_data.write(b'')
                        downloaded += 1
                        
                        # 发送进度更新（即使失败也计数）
                        download_progress_callback(video_id, title, downloaded, total)

            # 将文件指针移动到文件开头
            merged_data.seek(0)

            # 通知用户正在合并
            socketio.emit('download_progress', {
                'video_id': video_id,
                'title': title,
                'status': 'merging'
            })
            
            # 合并下载的切片文件（不使用ffmpeg）
            if downloader.merge_ts_files_binary(merged_data, f'{file_path}.mp4'):
                socketio.emit('download_progress', {
                    'video_id': video_id,
                    'title': title,
                    'status': 'completed'
                })
                return True
            else:
                socketio.emit('download_progress', {
                    'video_id': video_id,
                    'title': title,
                    'status': 'error',
                    'message': '视频合并失败'
                })
                return False
                
        except Exception as e:
            socketio.emit('download_progress', {
                'video_id': video_id,
                'title': title,
                'status': 'error',
                'message': f'下载视频时发生错误: {str(e)}'
            })
            return False
    except Exception as e:
        socketio.emit('download_progress', {
            'video_id': video_id,
            'title': title,
            'status': 'error',
            'message': f'下载过程中发生未知错误: {str(e)}'
        })
        return False

# 开始下载选中的视频
@app.route('/start_download', methods=['POST'])
def start_download():
    user_name = request.form['user_name']
    start_date = request.form['start_date']
    end_date = request.form['end_date']
    selected_videos = request.form.getlist('selected_videos')
    
    # 转换为整数索引
    selected_indices = [int(i) for i in selected_videos]
    
    # 创建下载器实例
    downloader = create_downloader(user_name)
    
    # 获取历史回放列表
    history_list = downloader.list_replays_by_date(start_date, end_date)
    
    # 在后台线程中执行下载任务
    def download_task():
        # 下载选中的视频
        if history_list and selected_indices:
            for i in selected_indices:
                if i < len(history_list):
                    room = history_list[i]
                    room_id = room['roomID']
                    title = room['roomTitle']
                    start_time = room['startTime']
                    
                    # 生成唯一的视频ID
                    import uuid
                    video_id = str(uuid.uuid4())
                    
                    # 执行下载
                    download_with_progress(
                        downloader,
                        room_id,
                        title,
                        start_time,
                        video_id,
                        DOWNLOAD_CONFIG['output_path'],
                        DOWNLOAD_CONFIG['processes']
                    )
    
    # 启动下载线程
    thread = threading.Thread(target=download_task)
    thread.daemon = True
    thread.start()
    
    # 立即返回响应
    return '', 204

if __name__ == '__main__':
    socketio.run(app, host='127.0.0.1', port=5000, debug=True)