# 抖音直播回放下载工具

这是一个用于下载抖音直播回放视频的Python工具。

## 功能特点

1. 自动获取抖音直播间回放信息
2. 支持动态生成必要的参数（msToken、a_bogus等)
3. 使用ffmpeg下载视频文件
4. 结构清晰，易于扩展和维护
5. 支持批量下载指定日期范围内的所有回放视频
6. 支持多进程并行下载，提高下载速度
7. 显示下载进度条，实时了解下载状态
8. 支持多用户配置，每个用户有自己的名字和Cookie
9. 支持配置文件，方便修改日期和Cookie等参数
10. 下载文件按"用户名-视频时间-标题"格式命名
11. 支持列出视频但不自动下载，可选择性下载特定视频

## 文件结构

- `main.py` - 主程序文件，包含下载器类和主要功能
- `dy_xb.py` - 用于生成X-Bogus签名的工具类
- `config.py` - 配置文件，包含多用户配置和下载设置
- `user_manager.py` - 用户管理工具，用于查看和切换用户
- `select_and_download.py` - 选择性下载工具，列出视频并允许用户选择下载
- `test_filename.py` - 文件名生成测试脚本
- `README.md` - 项目说明文档

## 多用户配置说明

在 `config.py` 文件中可以配置多个用户：

```python
# 多用户配置信息
USER_CONFIGS = {
    '用户1': {
        'cookie': 'your_cookie_for_user1_here',
    },
    
    '用户2': {
        'cookie': 'your_cookie_for_user2_here',
    },
    
    '用户3': {
        'cookie': 'your_cookie_for_user3_here',
    },
}

# 当前启用的用户
ACTIVE_USER = '用户1'
```

## 下载配置说明

```python
# 下载配置
DOWNLOAD_CONFIG = {
    # 需要下载的日期范围
    'start_date': '2025-08-04',
    'end_date': '2025-08-04',
    
    # 下载设置
    'output_path': './video',
    'use_multiprocess': True,
    'processes': 16,
}
```

## 文件命名格式

下载的视频文件将按照以下格式命名：
```
用户名-视频时间-标题.mp4
```

例如：
```
用户1-2025-08-04 10:55:03-划走你就大意了.mp4
```

文件名中的非法字符（如 `\ / : * ? " < > |`）将被替换为下划线 `_`。

## 使用方法

### 基本用法

```python
from main import DouyinReplayDownloader

# 创建下载器实例（使用配置文件中当前启用的用户）
downloader = DouyinReplayDownloader()

# 方法1: 使用配置文件中的设置下载视频
downloader.download_replays_with_config()

# 方法2: 指定特定用户创建下载器
from main import create_downloader
downloader = create_downloader('用户2')
downloader.download_replays_with_config()
```

### 列出并选择性下载视频

```bash
# 列出视频并允许选择性下载
python select_and_download.py

# 指定日期范围列出视频并允许选择性下载
python select_and_download.py 2025-08-01 2025-08-04
```

在选择性下载模式下，您可以：
- 输入序号下载特定视频（如：`1,3,5`）
- 输入范围下载一批视频（如：`1-5`）
- 混合输入下载（如：`1,3-5,7`）
- 输入`all`下载全部视频
- 输入`quit`退出程序

### 用户管理

```bash
# 列出所有用户
python user_manager.py list

# 显示当前配置
python user_manager.py show

# 切换当前启用的用户（仅当前会话有效）
python user_manager.py switch 用户2
```

### 测试文件名生成

```bash
# 测试文件名生成功能
python test_filename.py
```

### 命令行使用

直接运行脚本使用配置文件中的设置下载视频：

```bash
python3 main.py
```

然后取消注释需要执行的代码行来下载视频。

## 依赖项

- Python 3.x
- requests
- tqdm

## 安装依赖

```bash
pip install requests tqdm
```

确保系统已安装ffmpeg，并且可以在命令行中使用。

在Ubuntu/Debian上安装ffmpeg:
```bash
sudo apt update
sudo apt install ffmpeg
```

在macOS上安装ffmpeg:
```bash
brew install ffmpeg
```

在Windows上安装ffmpeg:
1. 访问 https://ffmpeg.org/download.html 下载Windows版本
2. 解压并添加到系统PATH环境变量中

## 输出文件

下载的视频将保存在项目目录下的`video`文件夹中，文件命名格式为`用户名-视频时间-标题.mp4`。

## 性能优化

1. 多进程下载：通过并行下载视频切片，显著提高下载速度
2. 进度显示：实时显示下载进度，让用户了解下载状态
3. 错误处理：对网络异常和下载错误进行处理，提高稳定性
4. 配置文件：将常用参数提取到配置文件中，方便修改和使用
5. 多用户支持：支持多个用户的配置，可以方便地切换用户
6. 智能命名：文件按"用户名-视频时间-标题"格式命名，便于识别和管理
7. 选择性下载：列出视频供用户选择，避免不必要的下载
8. 两种合并方式：默认使用纯Python二进制合并（无需额外依赖），也可选择使用ffmpeg进行合并

## 注意事项

1. 该工具仅用于学习和研究目的，请遵守相关法律法规和平台使用条款。
2. 视频链接可能有时效性，建议获取后尽快下载。
3. 部分直播房间可能没有回放功能或回放已被删除。
4. Cookie信息可能会过期，如遇到问题请检查Cookie是否有效。
5. 请合理使用下载功能，避免对服务器造成过大压力。
6. 多进程下载在某些操作系统上可能需要特殊配置，请根据实际情况选择合适的下载方式。
7. 当日期筛选出多个roomid时，会依次下载每个roomid里面的视频。
8. 多用户配置中，可以通过修改ACTIVE_USER来切换当前启用的用户。
9. 文件名中的非法字符会被自动替换为下划线，以确保文件可以正常保存。
10. 选择性下载功能允许用户在下载前预览所有可下载视频，并选择特定视频进行下载。
11. 现在支持两种视频合并方式：默认使用纯Python二进制合并（无需额外依赖），也可选择使用ffmpeg进行合并。