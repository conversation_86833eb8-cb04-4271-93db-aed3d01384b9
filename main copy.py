#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音直播回放下载脚本
"""

import requests
import random
import urllib.parse
import json
import os
import subprocess
from dy_xb import <PERSON>bogus
from multiprocessing import Pool
from tqdm import tqdm
from io import BytesIO
import re
from urllib.parse import urlparse, urlunparse
from config import USER_CONFIGS, ACTIVE_USER, DOWNLOAD_CONFIG


class DouyinReplayDownloader:
    def __init__(self, user_name=None):
        # 基础URL
        self.room_info_url = "https://anchor.douyin.com/webcast/api/platform_content_player/room/v1/get_room_info"
        self.history_list_url = "https://anchor.douyin.com/webcast/data/api/v1/component/lego/native/webcast_api/room/replay/history_list"
        
        # 从配置文件读取当前启用的用户Cookie
        self.user_name = user_name or ACTIVE_USER
        if self.user_name in USER_CONFIGS:
            self.headers = {
                "x-appid": "3000",
                "x-sub-web-id": "1116",
                "cookie": USER_CONFIGS[self.user_name]['cookie']
            }
        else:
            raise ValueError(f"用户 {self.user_name} 未在配置中找到")

    # 生成随机msToken
    def get_ms_token(self):
        sequence_str = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
        ms_token = ''
        for i in range(0, 128):
            r = random.randint(0, len(sequence_str)-1)
            ms_token += sequence_str[r]
        return ms_token

    # 生成a_bogus参数
    def get_a_bogus(self, params):
        # 使用Xbogus类生成a_bogus参数
        xbogus = Xbogus(params)
        return xbogus.getXBogus()

    # 获取历史回放列表
    def get_history_list(self, start_date, end_date):
        # 构建参数
        params = {
            "aid": "477650",
            "device_platform": "web",
            "version_name": "10000",
            "device_type": "web",
            "startDate": start_date,
            "endDate": end_date,
            "orderField": "startTimeUnix",
            "sortType": "desc",
            "needStats": "1",
            "limit": "400",
            "msToken": self.get_ms_token()
        }

        # 构建参数字符串用于生成a_bogus
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])

        # 生成a_bogus
        a_bogus = self.get_a_bogus(param_str)
        params["a_bogus"] = a_bogus

        # 构建完整URL
        url = self.history_list_url + "?" + "&".join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])

        # 添加特定请求头
        headers = self.headers.copy()
        headers.update({
            "accept": "application/json, text/plain, */*",
            "content-type": "application/json",
            "webid": "1116"
        })

        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                # 解析嵌套的JSON数据
                inner_data = json.loads(data["data"]["data"])
                return inner_data["data"]["series"]
            else:
                print(f"获取历史列表失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取历史列表时发生错误: {e}")
            return None

    # 获取房间信息
    def get_room_info(self, room_id):
        # 生成参数字符串
        params = {
            "room_id": room_id,
            "is_live": "false",
            "msToken": self.get_ms_token()
        }

        # 构建参数字符串用于生成a_bogus
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])

        # 生成a_bogus
        a_bogus = self.get_a_bogus(param_str)
        params["a_bogus"] = a_bogus

        # 构建完整URL
        url = self.room_info_url + "?" + "&".join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])

        try:
            response = requests.get(url, headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"请求发生错误: {e}")
            return None

    # 使用多进程下载视频
    def download_replay_multiprocess(self, room_id, title, start_time, output_path="./video", use_multiprocess=True, processes=16):
        # 获取房间信息
        room_info = self.get_room_info(room_id)
        
        if not room_info or room_info.get("status_code") != 0:
            print("获取房间信息失败")
            return False
            
        # 解析播放信息
        data = room_info.get("data", {})
        play_info = data.get("play_info", [])
        
        if not play_info:
            print("未找到回放视频信息")
            return False
            
        # 获取播放链接
        video_url = play_info[0].get("hls_url") or play_info[0].get("flv_url")
        if not video_url:
            print("未找到有效的视频播放链接")
            return False
            
        # 创建输出目录
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # 生成文件名（用户名-视频的时间-标题）
        # 清理标题中的非法字符
        safe_title = re.sub(r'[\\/:*?"<>|]', '_', title)
        filename = f"{self.user_name}-{start_time}-{safe_title}"
        file_path = os.path.join(output_path, filename)
        
        # 使用ffmpeg下载视频
        try:
            print(f"开始下载视频: {video_url}")
            print(f"保存路径: {file_path}.mp4")
            
            # 使用ffmpeg下载m3u8视频流，添加多个优化选项
            command = [
                'ffmpeg',
                '-i', video_url,                           # 输入URL
                '-c', 'copy',                              # 直接复制，不重新编码
                f'{file_path}.mp4'                         # 输出文件
            ]
            
            print(f"执行命令: {' '.join(command)}")
            
            # 实时显示FFmpeg输出
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # 实时显示FFmpeg输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
            
            # 检查执行结果
            return_code = process.poll()
            if return_code == 0:
                print(f"视频下载完成: {file_path}.mp4")
                return True
            else:
                print(f"视频下载失败，FFmpeg返回码: {return_code}")
                return False
                
        except Exception as e:
            print(f"下载视频时发生错误: {e}")
            return False

    # 批量下载指定日期范围内的所有回放视频
    def download_replays_by_date(self, start_date, end_date, output_path="./video", use_multiprocess=True, processes=16):
        # 获取历史回放列表
        history_list = self.get_history_list(start_date, end_date)
        
        if not history_list:
            print("未找到回放视频")
            return False
            
        print(f"找到 {len(history_list)} 个回放视频")
        
        # 逐个下载
        for i, room in enumerate(history_list):
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            print(f"\n开始下载第 {i+1}/{len(history_list)} 个视频:")
            print(f"房间ID: {room_id}")
            print(f"标题: {title}")
            print(f"开始时间: {start_time}")
            
            # 下载视频
            success = self.download_replay_multiprocess(room_id, title, start_time, output_path, use_multiprocess, processes)
                
            if success:
                print(f"视频下载成功: {title}")
            else:
                print(f"视频下载失败: {title}")
                
        print("所有视频下载完成")
        return True

    # 列出指定日期范围内的所有回放视频，但不下载
    def list_replays_by_date(self, start_date, end_date):
        # 获取历史回放列表
        history_list = self.get_history_list(start_date, end_date)
        
        if not history_list:
            print("未找到回放视频")
            return []
            
        print(f"找到 {len(history_list)} 个回放视频:")
        print("-" * 100)
        print(f"{'序号':<4} {'房间ID':<20} {'开始时间':<20} {'标题'}")
        print("-" * 100)
        
        for i, room in enumerate(history_list):
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            print(f"{i+1:<4} {room_id:<20} {start_time:<20} {title}")
            
        print("-" * 100)
        return history_list

    # 选择并下载指定的回放视频
    def select_and_download_replays(self, history_list, selected_indices, output_path="./video",use_multiprocess=True, processes=16):
        if not history_list:
            print("没有可下载的回放视频")
            return False
            
        # 验证选择的索引
        valid_indices = [i for i in selected_indices if 1 <= i <= len(history_list)]
        if not valid_indices:
            print("没有选择有效的视频序号")
            return False
            
        print(f"开始下载 {len(valid_indices)} 个选定的视频...")
        
        # 逐个下载选定的视频
        for i in valid_indices:
            room = history_list[i-1]  # 转换为0基索引
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            
            print(f"\n开始下载第 {i} 个视频:")
            print(f"房间ID: {room_id}")
            print(f"标题: {title}")
            print(f"开始时间: {start_time}")
            
            # 下载视频
            success = self.download_replay_multiprocess(room_id, title, start_time, output_path, use_multiprocess, processes)
                
            if success:
                print(f"视频下载成功: {title}")
            else:
                print(f"视频下载失败: {title}")
                
        print("选定视频下载完成")
        return True

# 根据用户名创建下载器的辅助函数
def create_downloader(user_name=None):
    """根据用户名创建下载器实例"""
    return DouyinReplayDownloader(user_name)


# 显示可用用户并让用户选择
def select_user():
    """让用户选择要使用的账号"""
    users = list(USER_CONFIGS.keys())
    
    if not users:
        print("配置文件中没有找到用户配置")
        return None
    
    print("可用的用户账号:")
    print("-" * 30)
    for i, user in enumerate(users, 1):
        active_marker = " (当前默认)" if user == ACTIVE_USER else ""
        print(f"{i}. {user}{active_marker}")
    print("-" * 30)
    
    while True:
        try:
            choice = input(f"请选择用户序号 (1-{len(users)})，或按回车使用默认用户 ({ACTIVE_USER}): ").strip()
            
            if choice == "":
                return ACTIVE_USER
                
            choice_num = int(choice)
            if 1 <= choice_num <= len(users):
                selected_user = users[choice_num - 1]
                print(f"已选择用户: {selected_user}")
                return selected_user
            else:
                print(f"请输入有效的序号 (1-{len(users)})")
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            return None


if __name__ == "__main__":
    # 让用户选择账号
    user_name = select_user()
    if user_name is None:
        print("未选择用户，程序退出")
    else:
        downloader = DouyinReplayDownloader(user_name)
        
        # 使用配置文件中的设置下载视频
        downloader.download_replays_with_config()