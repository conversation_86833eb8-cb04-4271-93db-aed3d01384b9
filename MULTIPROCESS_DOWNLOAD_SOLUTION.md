# 多进程下载m3u8视频解决方案

## 问题描述

原始代码在使用多进程下载m3u8视频时存在以下问题：

1. **固定空间预分配**：代码假设每个切片大小为2MB，预分配固定空间
2. **数据覆盖风险**：当切片实际大小超过2MB时，会覆盖下一个切片的预留空间
3. **不确定性问题**：无法预知每个切片的实际大小，导致文件损坏

## 解决方案

采用**分段下载方案**，将原来的"预分配固定空间"改为"独立块文件"的方式：

### 核心改进

1. **独立块文件**：每个进程将下载的切片写入独立的临时文件
2. **动态大小**：每个切片可以是任意大小，不受限制
3. **顺序合并**：所有进程完成后，按顺序合并块文件

### 修改的方法

#### 1. `download_replay_multiprocess` 方法

**原来的问题代码：**
```python
# 初始化临时文件（预分配空间）
with open(temp_file_path, 'wb') as temp_file:
    temp_file.seek(len(ts_url_list) * 2 * 1024 * 1024 - 1)  # 假设每个切片平均2MB
    temp_file.write(b'\0')
```

**修改后：**
```python
# 创建临时目录用于存储块文件
temp_dir = f'{file_path}_chunks'
if os.path.exists(temp_dir):
    shutil.rmtree(temp_dir)
os.makedirs(temp_dir)
```

#### 2. `download_chunk_to_file` 方法

**原来的问题代码：**
```python
# 计算写入位置（为每个切片预留2MB空间）
write_position = start_index * 2 * 1024 * 1024  # 每个切片预留2MB

# 直接写入到临时文件的指定位置
with open(temp_file_path, 'r+b') as temp_file:
    temp_file.seek(write_position)
    temp_file.write(video_data)

# 更新位置到下一个切片的预留位置
write_position += 2 * 1024 * 1024
```

**修改后：**
```python
# 创建块文件路径
chunk_file_path = os.path.join(temp_dir, f'chunk_{chunk_id}.tmp')

# 顺序写入切片数据，不需要计算固定位置
with open(chunk_file_path, 'wb') as chunk_file:
    for ts_url in ts_url_list:
        video_data = requests.get(ts_url, timeout=30).content
        chunk_file.write(video_data)  # 直接追加写入
```

#### 3. 新增 `merge_chunk_files` 方法

```python
def merge_chunk_files(self, temp_dir, chunk_count, final_file_path):
    """按顺序合并所有块文件到最终文件"""
    try:
        with open(final_file_path, 'wb') as final_file:
            for chunk_id in range(chunk_count):
                chunk_file_path = os.path.join(temp_dir, f'chunk_{chunk_id}.tmp')
                
                if not os.path.exists(chunk_file_path):
                    print(f"警告: 块文件 {chunk_file_path} 不存在")
                    continue
                
                # 读取块文件并写入最终文件
                with open(chunk_file_path, 'rb') as chunk_file:
                    while True:
                        data = chunk_file.read(8192)  # 8KB 缓冲区
                        if not data:
                            break
                        final_file.write(data)
        return True
    except Exception as e:
        print(f"合并块文件时发生错误: {e}")
        return False
```

## 方案优势

### 1. 解决切片大小不确定问题
- ✅ 支持任意大小的切片（1KB到10MB+都没问题）
- ✅ 不再有数据覆盖风险
- ✅ 不需要预知切片大小

### 2. 保持性能优势
- ✅ 保持多进程并发下载
- ✅ 内存使用更加合理
- ✅ 磁盘I/O优化（顺序写入）

### 3. 提高可靠性
- ✅ 每个进程独立工作，互不干扰
- ✅ 更好的错误处理和恢复
- ✅ 临时文件自动清理

## 测试验证

运行 `test_multiprocess_download.py` 进行验证：

```bash
python3 test_multiprocess_download.py
```

测试包括：
1. **基本功能测试**：验证块下载和合并功能
2. **大小灵活性测试**：验证处理不同大小切片的能力（包括超过原来2MB限制的切片）

## 使用方法

修改后的代码使用方法完全不变：

```python
# 创建下载器
downloader = DouyinReplayDownloader()

# 使用多进程下载（现在支持任意大小的切片）
success = downloader.download_replay_multiprocess(
    room_id="123456", 
    title="测试视频", 
    start_time="2024-01-01", 
    output_path="./video", 
    processes=16
)
```

## 注意事项

1. **临时目录**：下载过程中会创建临时目录 `{filename}_chunks`，完成后自动清理
2. **磁盘空间**：需要确保有足够的磁盘空间存储临时块文件
3. **进程数量**：建议根据网络带宽和CPU核心数调整进程数量

## 总结

这个解决方案彻底解决了原来固定预分配空间导致的问题，现在可以：
- 处理任意大小的视频切片
- 避免数据覆盖和文件损坏
- 保持高性能的多进程下载
- 提供更好的错误处理和恢复机制

修改后的代码更加健壮和可靠，能够应对各种实际场景中的切片大小变化。
