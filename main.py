#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音直播回放下载脚本
"""

import requests
import random
import urllib.parse
import json
import os
import subprocess
from dy_xb import <PERSON>bogus
from multiprocessing import Pool
from tqdm import tqdm
from io import BytesIO
import re
from urllib.parse import urlparse, urlunparse
from config import USER_CONFIGS, ACTIVE_USER, DOWNLOAD_CONFIG


class DouyinReplayDownloader:
    def __init__(self, user_name=None):
        # 基础URL
        self.room_info_url = "https://anchor.douyin.com/webcast/api/platform_content_player/room/v1/get_room_info"
        self.history_list_url = "https://anchor.douyin.com/webcast/data/api/v1/component/lego/native/webcast_api/room/replay/history_list"
        
        # 从配置文件读取当前启用的用户Cookie
        self.user_name = user_name or ACTIVE_USER
        if self.user_name in USER_CONFIGS:
            self.headers = {
                "x-appid": "3000",
                "x-sub-web-id": "1116",
                "cookie": USER_CONFIGS[self.user_name]['cookie']
            }
        else:
            raise ValueError(f"用户 {self.user_name} 未在配置中找到")

    # 生成随机msToken
    def get_ms_token(self):
        sequence_str = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
        ms_token = ''
        for i in range(0, 128):
            r = random.randint(0, len(sequence_str)-1)
            ms_token += sequence_str[r]
        return ms_token

    # 生成a_bogus参数
    def get_a_bogus(self, params):
        # 使用Xbogus类生成a_bogus参数
        xbogus = Xbogus(params)
        return xbogus.getXBogus()

    # 获取历史回放列表
    def get_history_list(self, start_date, end_date):
        # 构建参数
        params = {
            "aid": "477650",
            "device_platform": "web",
            "version_name": "10000",
            "device_type": "web",
            "startDate": start_date,
            "endDate": end_date,
            "orderField": "startTimeUnix",
            "sortType": "desc",
            "needStats": "1",
            "limit": "400",
            "msToken": self.get_ms_token()
        }

        # 构建参数字符串用于生成a_bogus
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])

        # 生成a_bogus
        a_bogus = self.get_a_bogus(param_str)
        params["a_bogus"] = a_bogus

        # 构建完整URL
        url = self.history_list_url + "?" + "&".join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])

        # 添加特定请求头
        headers = self.headers.copy()
        headers.update({
            "accept": "application/json, text/plain, */*",
            "content-type": "application/json",
            "webid": "1116"
        })

        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                # 解析嵌套的JSON数据
                inner_data = json.loads(data["data"]["data"])
                return inner_data["data"]["series"]
            else:
                print(f"获取历史列表失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取历史列表时发生错误: {e}")
            return None

    # 获取房间信息
    def get_room_info(self, room_id):
        # 生成参数字符串
        params = {
            "room_id": room_id,
            "is_live": "false",
            "msToken": self.get_ms_token()
        }

        # 构建参数字符串用于生成a_bogus
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])

        # 生成a_bogus
        a_bogus = self.get_a_bogus(param_str)
        params["a_bogus"] = a_bogus

        # 构建完整URL
        url = self.room_info_url + "?" + "&".join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])

        try:
            response = requests.get(url, headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"请求发生错误: {e}")
            return None

    # 获取文件切片地址的函数
    def get_ts_list(self, url):
        response = requests.get(url)
        ts_list = re.sub('#E.*', '', response.text).split()
        return ts_list

    # 获取ts切片的地址域名
    def get_base_url(self, url, just_m_domain):
        # 解析URL
        parsed_url = urlparse(url)

        # 从路径中获取目录部分
        if just_m_domain:
            directory = ''
        else:
            path_parts = parsed_url.path.split('/')
            directory = '/'.join(path_parts[:-1])

        # 重新构建URL（去除文件名）
        new_url = urlunparse(
            (parsed_url.scheme, parsed_url.netloc, directory, '', '', ''))
        return new_url

    # 下载单个切片的函数
    def download_ts(self, ts_url):
        try:
            video_data = requests.get(ts_url, timeout=30).content
            return [video_data, ts_url]
        except Exception as e:
            print(f"下载切片失败 {ts_url}: {e}")
            # 如果发生错误，尝试重新下载
            return self.download_ts(ts_url)

    # 使用多进程下载视频
    def download_replay_multiprocess(self, room_id, title, start_time, output_path="./video", processes=16):
        # 获取房间信息
        room_info = self.get_room_info(room_id)
        
        if not room_info or room_info.get("status_code") != 0:
            print("获取房间信息失败")
            return False
            
        # 解析播放信息
        data = room_info.get("data", {})
        play_info = data.get("play_info", [])
        
        if not play_info:
            print("未找到回放视频信息")
            return False
            
        # 获取播放链接
        m3u8_url = play_info[0].get("hls_url") or play_info[0].get("flv_url")
        if not m3u8_url:
            print("未找到有效的视频播放链接")
            return False
            
        # 创建输出目录
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # 生成文件名（用户名-视频的时间-标题）
        # 清理标题中的非法字符
        safe_title = re.sub(r'[\\/:*?"<>|]', '_', title)
        filename = f"{self.user_name}-{start_time}-{safe_title}"
        file_path = os.path.join(output_path, filename)
        final_file_path = f'{file_path}.mp4'
        
        try:
            print(f"开始下载视频: {m3u8_url}")
            print(f"保存路径: {final_file_path}")
            
            # 获取m3u8文件切片
            ts_list = self.get_ts_list(m3u8_url)
            just_domain = ts_list[0][0] == '/'
            base_url = self.get_base_url(m3u8_url, just_domain) + '/'
            ts_list = [s.lstrip('/') for s in ts_list]
            ts_url_list = [base_url + item for item in ts_list]

            print(f"总共需要下载 {len(ts_url_list)} 个切片")
            
            # 将任务分块，每个进程处理一个大块
            chunk_size = len(ts_url_list) // processes
            if len(ts_url_list) % processes != 0:
                chunk_size += 1
                
            chunks = []
            for i in range(processes):
                start_index = i * chunk_size
                end_index = min((i + 1) * chunk_size, len(ts_url_list))
                if start_index < len(ts_url_list):
                    chunks.append((i, start_index, end_index, ts_url_list[start_index:end_index]))
            
            print(f"将任务分为 {len(chunks)} 个块，每块大约 {chunk_size} 个切片")
            
            # 创建临时文件用于直接写入磁盘
            temp_file_path = f'{file_path}.tmp'
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                
            # 初始化临时文件（预分配空间）
            with open(temp_file_path, 'wb') as temp_file:
                temp_file.seek(len(ts_url_list) * 2 * 1024 * 1024 - 1)  # 假设每个切片平均2MB
                temp_file.write(b'\0')
            
            # 创建一个进程池，设置并行下载的进程数
            with Pool(processes) as pool:
                # 提交任务到进程池
                results = []
                for chunk_info in chunks:
                    chunk_id, start_idx, end_idx, chunk_urls = chunk_info
                    result = pool.apply_async(self.download_chunk_to_file, 
                                            (chunk_id, start_idx, chunk_urls, temp_file_path, len(ts_list)))
                    results.append(result)
                
                # 等待所有任务完成并显示进度
                with tqdm(total=len(chunks), desc="下载进度") as pbar:
                    for result in results:
                        result.get()  # 等待任务完成
                        pbar.update(1)
            
            # 截断文件到实际大小并重命名
            actual_size = 0
            with open(temp_file_path, 'rb') as f:
                f.seek(0, 2)  # 移动到文件末尾
                # 从后往前查找最后一个非空字节
                buf_size = 8192
                end_pos = f.tell()
                while end_pos > 0 and actual_size == 0:
                    read_size = min(buf_size, end_pos)
                    end_pos -= read_size
                    f.seek(end_pos)
                    buf = f.read(read_size)
                    # 查找最后一个非空字节
                    for i in range(len(buf) - 1, -1, -1):
                        if buf[i] != 0:
                            actual_size = end_pos + i + 1
                            break
            
            # 调整文件大小
            with open(temp_file_path, 'r+b') as temp_file:
                temp_file.truncate(actual_size)
            
            # 重命名临时文件为最终文件
            os.rename(temp_file_path, final_file_path)
            print(f"视频下载完成: {final_file_path}")
            return True
                
        except Exception as e:
            print(f"下载视频时发生错误: {e}")
            # 清理临时文件
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            return False

    # 下载一个块的数据
    def download_chunk(self, chunk_id, ts_url_list):
        """
        下载一个块中的所有切片
        :param chunk_id: 块ID
        :param ts_url_list: 需要下载的切片URL列表
        :return: 下载的数据列表
        """
        chunk_data = []
        for ts_url in ts_url_list:
            video_data = self.download_ts(ts_url)[0]
            chunk_data.append(video_data)
            
        return chunk_data

    # 下载一个块的数据并直接写入文件指定位置
    def download_chunk_to_file(self, chunk_id, start_index, ts_url_list, temp_file_path, total_ts_count):
        """
        下载一个块中的所有切片并直接写入到文件的指定位置
        :param chunk_id: 块ID
        :param start_index: 在完整切片列表中的起始索引
        :param ts_url_list: 需要下载的切片URL列表
        :param temp_file_path: 临时文件路径
        :param total_ts_count: 总的切片数量
        :return: None
        """
        # 计算写入位置（为每个切片预留2MB空间）
        write_position = start_index * 2 * 1024 * 1024  # 每个切片预留2MB
        
        for i, ts_url in enumerate(ts_url_list):
            try:
                # 下载切片数据
                video_data = requests.get(ts_url, timeout=30).content
                
                # 直接写入到临时文件的指定位置
                with open(temp_file_path, 'r+b') as temp_file:
                    temp_file.seek(write_position)
                    temp_file.write(video_data)
                
                # 更新位置到下一个切片的预留位置
                write_position += 2 * 1024 * 1024
                
            except Exception as e:
                print(f"下载切片失败 {ts_url}: {e}")
                # 如果发生错误，尝试重新下载
                try:
                    video_data = requests.get(ts_url, timeout=30).content
                    with open(temp_file_path, 'r+b') as temp_file:
                        temp_file.seek(write_position)
                        temp_file.write(video_data)
                    write_position += 2 * 1024 * 1024
                except Exception as e2:
                    print(f"重新下载切片仍失败 {ts_url}: {e2}")
                    write_position += 2 * 1024 * 1024

    def merge_ts_files_binary(self, merged_data, output_file):
        """
        使用二进制方式合并TS文件，不依赖ffmpeg
        """
        try:
            with open(output_file, 'wb') as outfile:
                outfile.write(merged_data.getvalue())
            return True
        except Exception as e:
            print(f"合并TS文件时发生错误: {e}")
            return False

    # 使用ffmpeg下载视频（简单方式）
    def download_replay(self, room_id, title, start_time, output_path="./video"):
        # 获取房间信息
        room_info = self.get_room_info(room_id)
        
        if not room_info or room_info.get("status_code") != 0:
            print("获取房间信息失败")
            return False
            
        # 解析播放信息
        data = room_info.get("data", {})
        play_info = data.get("play_info", [])
        
        if not play_info:
            print("未找到回放视频信息")
            return False
            
        # 获取播放链接
        video_url = play_info[0].get("hls_url") or play_info[0].get("flv_url")
        if not video_url:
            print("未找到有效的视频播放链接")
            return False
            
        # 创建输出目录
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # 生成文件名（用户名-视频的时间-标题）
        # 清理标题中的非法字符
        safe_title = re.sub(r'[\\/:*?"<>|]', '_', title)
        filename = f"{self.user_name}-{start_time}-{safe_title}"
        file_path = os.path.join(output_path, filename)
        
        # 使用ffmpeg下载视频
        try:
            print(f"开始下载视频: {video_url}")
            print(f"保存路径: {file_path}.mp4")
            
            # 使用ffmpeg下载m3u8视频流
            command = ['ffmpeg', '-i', video_url, '-c', 'copy', f'{file_path}.mp4']
            result = subprocess.run(command, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            if result.returncode == 0:
                print(f"视频下载完成: {file_path}.mp4")
                return True
            else:
                print(f"视频下载失败: {result.stderr.decode('utf-8')}")
                return False
        except Exception as e:
            print(f"下载视频时发生错误: {e}")
            return False

    # 批量下载指定日期范围内的所有回放视频
    def download_replays_by_date(self, start_date, end_date, output_path="./video", use_multiprocess=False, processes=16):
        # 获取历史回放列表
        history_list = self.get_history_list(start_date, end_date)
        
        if not history_list:
            print("未找到回放视频")
            return False
            
        print(f"找到 {len(history_list)} 个回放视频")
        
        # 逐个下载
        for i, room in enumerate(history_list):
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            print(f"\n开始下载第 {i+1}/{len(history_list)} 个视频:")
            print(f"房间ID: {room_id}")
            print(f"标题: {title}")
            print(f"开始时间: {start_time}")
            
            # 下载视频
            if use_multiprocess:
                success = self.download_replay_multiprocess(room_id, title, start_time, output_path, processes)
            else:
                success = self.download_replay(room_id, title, start_time, output_path)
                
            if success:
                print(f"视频下载成功: {title}")
            else:
                print(f"视频下载失败: {title}")
                
        print("所有视频下载完成")
        return True

    # 列出指定日期范围内的所有回放视频，但不下载
    def list_replays_by_date(self, start_date, end_date):
        # 获取历史回放列表
        history_list = self.get_history_list(start_date, end_date)
        
        if not history_list:
            print("未找到回放视频")
            return []
            
        print(f"找到 {len(history_list)} 个回放视频:")
        print("-" * 100)
        print(f"{'序号':<4} {'房间ID':<20} {'开始时间':<20} {'标题'}")
        print("-" * 100)
        
        for i, room in enumerate(history_list):
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            print(f"{i+1:<4} {room_id:<20} {start_time:<20} {title}")
            
        print("-" * 100)
        return history_list

    # 选择并下载指定的回放视频
    def select_and_download_replays(self, history_list, selected_indices, output_path="./video", use_multiprocess=False, processes=16):
        if not history_list:
            print("没有可下载的回放视频")
            return False
            
        # 验证选择的索引
        valid_indices = [i for i in selected_indices if 1 <= i <= len(history_list)]
        if not valid_indices:
            print("没有选择有效的视频序号")
            return False
            
        print(f"开始下载 {len(valid_indices)} 个选定的视频...")
        
        # 逐个下载选定的视频
        for i in valid_indices:
            room = history_list[i-1]  # 转换为0基索引
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            
            print(f"\n开始下载第 {i} 个视频:")
            print(f"房间ID: {room_id}")
            print(f"标题: {title}")
            print(f"开始时间: {start_time}")
            
            # 下载视频
            if use_multiprocess:
                success = self.download_replay_multiprocess(room_id, title, start_time, output_path, processes)
            else:
                success = self.download_replay(room_id, title, start_time, output_path)
                
            if success:
                print(f"视频下载成功: {title}")
            else:
                print(f"视频下载失败: {title}")
                
        print("选定视频下载完成")
        return True

    # 使用配置文件中的设置下载视频
    def download_replays_with_config(self):
        start_date = DOWNLOAD_CONFIG['start_date']
        end_date = DOWNLOAD_CONFIG['end_date']
        output_path = DOWNLOAD_CONFIG['output_path']
        use_multiprocess = DOWNLOAD_CONFIG['use_multiprocess']
        processes = DOWNLOAD_CONFIG['processes']
        
        print(f"使用用户: {self.user_name}")
        print(f"开始下载 {start_date} 到 {end_date} 期间的回放视频")
        return self.download_replays_by_date(
            start_date, 
            end_date, 
            output_path, 
            use_multiprocess, 
            processes
        )


# 根据用户名创建下载器的辅助函数
def create_downloader(user_name=None):
    """根据用户名创建下载器实例"""
    return DouyinReplayDownloader(user_name)


# 显示可用用户并让用户选择
def select_user():
    """让用户选择要使用的账号"""
    users = list(USER_CONFIGS.keys())
    
    if not users:
        print("配置文件中没有找到用户配置")
        return None
    
    print("可用的用户账号:")
    print("-" * 30)
    for i, user in enumerate(users, 1):
        active_marker = " (当前默认)" if user == ACTIVE_USER else ""
        print(f"{i}. {user}{active_marker}")
    print("-" * 30)
    
    while True:
        try:
            choice = input(f"请选择用户序号 (1-{len(users)})，或按回车使用默认用户 ({ACTIVE_USER}): ").strip()
            
            if choice == "":
                return ACTIVE_USER
                
            choice_num = int(choice)
            if 1 <= choice_num <= len(users):
                selected_user = users[choice_num - 1]
                print(f"已选择用户: {selected_user}")
                return selected_user
            else:
                print(f"请输入有效的序号 (1-{len(users)})")
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            return None


if __name__ == "__main__":
    # 让用户选择账号
    user_name = select_user()
    if user_name is None:
        print("未选择用户，程序退出")
    else:
        downloader = DouyinReplayDownloader(user_name)
        
        # 使用配置文件中的设置下载视频
        downloader.download_replays_with_config()