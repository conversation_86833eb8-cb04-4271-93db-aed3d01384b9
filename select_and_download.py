#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列出指定日期范围内的抖音回放视频，并允许用户选择要下载的视频
"""

import sys
from main import create_downloader
from config import DOWNLOAD_CONFIG, USER_CONFIGS
from datetime import datetime

def select_user():
    """让用户选择要使用的账号"""
    users = list(USER_CONFIGS.keys())
    
    if not users:
        print("配置文件中没有找到用户配置")
        return None
    
    print("可用的用户账号:")
    print("-" * 30)
    for i, user in enumerate(users, 1):
        print(f"{i}. {user}")
    print("-" * 30)
    
    while True:
        try:
            choice = input(f"请选择用户序号 (1-{len(users)}) 或输入'quit'退出: ").strip()
            
            if choice.lower() == 'quit':
                return None
                
            choice_num = int(choice)
            if 1 <= choice_num <= len(users):
                selected_user = users[choice_num - 1]
                print(f"已选择用户: {selected_user}")
                return selected_user
            else:
                print(f"请输入有效的序号 (1-{len(users)})")
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            return None

def main():
    # 获取日期参数
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        # 使用今天的日期
        today = datetime.now().strftime('%Y-%m-%d')
        start_date = today
        end_date = today
        print(f"未指定日期，默认使用今天: {today}")
    
    # 让用户选择账号
    user_name = select_user()
    if user_name is None:
        print("未选择用户，程序退出")
        return
    
    # 创建下载器实例
    downloader = create_downloader(user_name)
    print(f"使用用户: {downloader.user_name}")
    print(f"查询日期范围: {start_date} 到 {end_date}")
    
    # 列出回放视频
    history_list = downloader.list_replays_by_date(start_date, end_date)
    
    if not history_list:
        print("在指定日期范围内没有找到回放视频")
        return
    
    # 询问用户选择要下载的视频
    while True:
        try:
            user_input = input("\n请输入要下载的视频序号（多个序号用逗号分隔，输入'all'下载全部，输入'quit'退出）: ").strip()
            
            if user_input.lower() == 'quit':
                print("退出程序")
                break
            elif user_input.lower() == 'all':
                # 下载全部视频
                print("开始下载全部视频...")
                downloader.download_replays_by_date(
                    start_date, 
                    end_date, 
                    DOWNLOAD_CONFIG['output_path'],
                    DOWNLOAD_CONFIG['use_multiprocess'],
                    DOWNLOAD_CONFIG['processes']
                )
                break
            elif user_input:
                # 解析用户输入的序号
                selected_indices = []
                for part in user_input.split(','):
                    part = part.strip()
                    if '-' in part:
                        # 处理范围输入，如"1-5"
                        start, end = part.split('-')
                        selected_indices.extend(range(int(start), int(end) + 1))
                    else:
                        # 单个数字
                        selected_indices.append(int(part))
                
                # 去重并排序
                selected_indices = sorted(list(set(selected_indices)))
                
                # 显示用户选择的序号
                print(f"您选择下载的视频序号: {selected_indices}")
                
                # 下载选定的视频
                success = downloader.select_and_download_replays(
                    history_list,
                    selected_indices,
                    DOWNLOAD_CONFIG['output_path'],
                    DOWNLOAD_CONFIG['use_multiprocess'],
                    DOWNLOAD_CONFIG['processes']
                )
                
                if success:
                    print("下载完成")
                    break
            else:
                print("输入为空，请重新输入")
                
        except ValueError:
            print("输入格式错误，请输入数字序号，多个序号用逗号分隔")
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()